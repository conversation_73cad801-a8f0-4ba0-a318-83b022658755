"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { Shell } from "@/components/shell"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"
import { DealDetailData } from "@/lib/types/deal-detail"
import { useAuth } from "@/lib/auth-context"
import { DealHeader } from "@/components/core/deals/deal-detail/deal-header"
import { DealTabs } from "@/components/core/deals/deal-detail/deal-tabs"
import { AiChat } from "@/components/core/deals/deal-detail/ai-chat"
import { OrbitAI } from "@/components/core/orbit-ai/orbit-ai"

import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"

export default function DealDetailPage() {
  const params = useParams()
  const dealId = params?.id as string
  const { isAuthenticated } = useAuth()

  const [dealData, setDealData] = useState<DealDetailData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('score') // Start with score tab for magic moment

  // Validate deal ID
  if (!dealId || dealId === 'undefined') {
    console.error('Invalid deal ID:', dealId);
  }

  useEffect(() => {
    if (!isAuthenticated || !dealId || dealId === 'undefined') {
      if (dealId === 'undefined') {
        setError('Invalid deal ID. Please check the URL and try again.');
        setLoading(false);
      }
      return;
    }

    const fetchDealDetail = async () => {
      try {
        setLoading(true)
        setError(null)

        console.log('Fetching deal detail for ID:', dealId);
        const data = await DealDetailAPI.getDealDetail(dealId)
        setDealData(data)
      } catch (err: any) {
        console.error('Error fetching deal detail:', err)
        setError('Failed to load deal details. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    fetchDealDetail()
  }, [isAuthenticated, dealId])

  // Loading state
  if (loading) {
    return (
      <>
        <div className="min-h-screen bg-background">
          <div className="container mx-auto px-4 lg:px-8 py-6">
            <div className="space-y-8">
              {/* Header skeleton */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-10 w-80" />
                    <Skeleton className="h-6 w-48" />
                  </div>
                  <div className="flex gap-2">
                    <Skeleton className="h-10 w-40" />
                    <Skeleton className="h-10 w-32" />
                  </div>
                </div>
              </div>
              
              {/* Tabs skeleton */}
              <div className="space-y-8">
                <div className="grid grid-cols-6 gap-4 p-1 bg-muted/30 rounded-lg border">
                  {Array.from({ length: 6 }).map((_, i) => (
                    <Skeleton key={i} className="h-14 w-full rounded-md" />
                  ))}
                </div>
                
                {/* Content skeleton */}
                <div className="space-y-6">
                  {Array.from({ length: 3 }).map((_, i) => (
                    <Skeleton key={i} className="h-32 w-full rounded-lg" />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Orbit AI - Always visible even during loading */}
        <OrbitAI
          dealId={dealId}
          dealContext={null}
        />
      </>
    )
  }

  // Error state
  if (error) {
    return (
      <>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Alert variant="destructive" className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
            </AlertDescription>
          </Alert>
        </div>
        
        {/* Orbit AI - Always visible even during error */}
        <OrbitAI
          dealId={dealId}
          dealContext={null}
        />
      </>
    )
  }

  // No data state
  if (!dealData) {
    return (
      <>
        <div className="min-h-screen bg-background flex items-center justify-center">
          <Alert className="max-w-md mx-auto">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Deal not found.
            </AlertDescription>
          </Alert>
        </div>
        
        {/* Orbit AI - Always visible even when no data */}
        <OrbitAI
          dealId={dealId}
          dealContext={null}
        />
      </>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Deal Header - Full Width */}
      <div className="bg-white border-b">
        <div className="max-w-screen-xl mx-auto px-8 py-6">
          <DealHeader deal={dealData} />
        </div>
      </div>

      {/* Main Content - Full Width Tabs Only */}
      <div className="w-full">
        <div className="max-w-screen-xl mx-auto">
          <DealTabs
            deal={dealData}
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />
        </div>
      </div>

      {/* Orbit AI - Floating Assistant Only */}
      <OrbitAI
        dealId={dealId}
        dealContext={dealData}
      />
    </div>
  )
}
