"use client"

import { dashboardConfig } from "@/config/dashboard"
import { MainNav } from "@/components/main-nav"
import { DashboardNav } from "@/components/nav"
import { SiteFooter } from "@/components/site-footer"
import { UserAccountNav } from "@/components/user-account-nav"
import { ProtectedRoute } from "@/components/protected-route"
import { OrganizationSelector } from "@/components/org-selector"
import { useAuth } from "@/lib/auth-context"
import { useState } from "react"
import { Icons } from "@/components/icons"

interface DashboardLayoutProps {
  children?: React.ReactNode
}

export default function DashboardLayout({
  children,
}: DashboardLayoutProps) {
  const { user } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col space-y-6">
        <header className="sticky top-0 z-40 border-b bg-background">
          <div className="container flex h-16 items-center justify-between py-4">
            <div className="flex items-center gap-4">
              <MainNav items={dashboardConfig.mainNav} />
              <OrganizationSelector />
            </div>
            {user && (
              <UserAccountNav
                user={{
                  name: user.name,
                  image: null,
                  email: user.email,
                }}
              />
            )}
          </div>
        </header>
        <div className="container grid flex-1 gap-12 md:grid-cols-[auto_1fr]">
          {/* Sidebar with collapse/expand toggle */}
          <aside className={`relative flex-col transition-all duration-300 ${sidebarOpen ? 'w-[200px] md:flex' : 'w-0 md:w-12'} hidden md:flex`}> 
            <button
              className="absolute -right-4 top-4 z-20 bg-background border border-border rounded-full p-1 shadow hover:bg-muted transition-colors"
              onClick={() => setSidebarOpen((open) => !open)}
              aria-label={sidebarOpen ? 'Collapse sidebar' : 'Expand sidebar'}
              type="button"
            >
              {sidebarOpen ? (
                <Icons.chevronLeft className="h-5 w-5" />
              ) : (
                <Icons.chevronRight className="h-5 w-5" />
              )}
            </button>
            <div className={`transition-all duration-300 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}>
          <DashboardNav items={dashboardConfig.sidebarNav} />
            </div>
        </aside>
        <main className="flex w-full flex-1 flex-col overflow-x-hidden">
          {children}
        </main>
      </div>
      <SiteFooter className="border-t" />
    </div>
    </ProtectedRoute>
  )
}
