import * as React from "react"

import { cn } from "@/lib/utils"

interface DashboardShellProps extends React.HTMLAttributes<HTMLDivElement> {}

export function DashboardShell({
  children,
  className,
  ...props
}: DashboardShellProps) {
  return (
    <div className={cn("w-full", className)} {...props}>
      <div className="max-w-screen-xl mx-auto px-8 py-8 space-y-8">
        {children}
      </div>
    </div>
  )
}

// Export Shell as an alias for DashboardShell for backward compatibility
export const Shell = DashboardShell;
