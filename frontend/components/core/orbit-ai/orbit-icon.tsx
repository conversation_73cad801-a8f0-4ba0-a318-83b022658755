"use client"

import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

interface OrbitIconProps {
  className?: string
  animated?: boolean
  glowing?: boolean
}

export function OrbitIcon({ className, animated = false, glowing = false }: OrbitIconProps) {
  return (
    <motion.div
      className={cn("relative", className)}
      animate={animated ? {
        rotate: [0, 360],
        scale: [1, 1.1, 1]
      } : {}}
      transition={animated ? {
        rotate: { duration: 8, repeat: Infinity, ease: "linear" },
        scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
      } : {}}
    >
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={cn(
          "text-primary",
          glowing && "drop-shadow-[0_0_8px_currentColor]"
        )}
      >
        {/* Central Core */}
        <motion.circle
          cx="12"
          cy="12"
          r="2"
          fill="currentColor"
          animate={animated ? {
            scale: [1, 1.2, 1],
            opacity: [0.8, 1, 0.8]
          } : {}}
          transition={animated ? {
            duration: 1.5,
            repeat: Infinity,
            ease: "easeInOut"
          } : {}}
        />
        
        {/* Orbital Rings */}
        <motion.circle
          cx="12"
          cy="12"
          r="6"
          stroke="currentColor"
          strokeWidth="1"
          fill="none"
          opacity="0.6"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={animated ? {
            duration: 4,
            repeat: Infinity,
            ease: "linear"
          } : {}}
        />
        
        <motion.circle
          cx="12"
          cy="12"
          r="9"
          stroke="currentColor"
          strokeWidth="0.5"
          fill="none"
          opacity="0.4"
          animate={animated ? {
            rotate: [360, 0]
          } : {}}
          transition={animated ? {
            duration: 6,
            repeat: Infinity,
            ease: "linear"
          } : {}}
        />
        
        {/* Orbital Dots */}
        <motion.circle
          cx="18"
          cy="12"
          r="1"
          fill="currentColor"
          opacity="0.8"
          animate={animated ? {
            rotate: [0, 360]
          } : {}}
          transition={animated ? {
            duration: 4,
            repeat: Infinity,
            ease: "linear"
          } : {}}
          style={{ transformOrigin: "12px 12px" }}
        />
        
        <motion.circle
          cx="12"
          cy="3"
          r="0.5"
          fill="currentColor"
          opacity="0.6"
          animate={animated ? {
            rotate: [360, 0]
          } : {}}
          transition={animated ? {
            duration: 6,
            repeat: Infinity,
            ease: "linear"
          } : {}}
          style={{ transformOrigin: "12px 12px" }}
        />
        
        {/* Energy Lines */}
        <motion.path
          d="M12 2 L12 6 M12 18 L12 22 M2 12 L6 12 M18 12 L22 12"
          stroke="currentColor"
          strokeWidth="0.5"
          opacity="0.3"
          animate={animated ? {
            opacity: [0.3, 0.7, 0.3]
          } : {}}
          transition={animated ? {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          } : {}}
        />
      </svg>
      
      {/* Glow Effect */}
      {glowing && (
        <motion.div
          className="absolute inset-0 rounded-full bg-primary/20 blur-md"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 0.8, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      )}
    </motion.div>
  )
}
