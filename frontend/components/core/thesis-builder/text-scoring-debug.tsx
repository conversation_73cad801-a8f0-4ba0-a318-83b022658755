'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RuleEditor } from './rule-editor';
import { QuestionType, RuleType, ConditionOperator } from '@/lib/types/thesis';
import type { ScoringRule } from '@/lib/types/thesis';

/**
 * Debug component to test text-based scoring rule functionality
 * This helps verify that the fixes for text question scoring are working correctly
 */
export function TextScoringDebug() {
  const [showEditor, setShowEditor] = useState(false);
  const [savedRule, setSavedRule] = useState<Partial<ScoringRule> | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  const handleSaveRule = (rule: Partial<ScoringRule>) => {
    addLog('🚀 Rule save triggered!');
    addLog(`Rule data: ${JSON.stringify(rule, null, 2)}`);
    
    // Validate the rule structure
    if (rule.condition && 'value' in rule.condition) {
      const value = rule.condition.value;
      if (typeof value === 'object' && value !== null) {
        addLog(`✅ Condition value is object: ${JSON.stringify(value)}`);
        if (value.good_reference || value.bad_reference) {
          addLog('✅ Text references found in condition.value');
        } else {
          addLog('❌ No text references in condition.value');
        }
      } else {
        addLog(`❌ Condition value is not object: ${typeof value}`);
      }
    } else {
      addLog('❌ No condition.value found');
    }
    
    setSavedRule(rule);
    setShowEditor(false);
    addLog('✅ Rule saved successfully');
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="space-y-6 p-6">
      <Card>
        <CardHeader>
          <CardTitle>Text Scoring Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Button 
              onClick={() => {
                addLog('Opening text question rule editor...');
                setShowEditor(true);
              }}
            >
              Test Text Question Scoring
            </Button>
            <Button variant="outline" onClick={clearLogs}>
              Clear Logs
            </Button>
          </div>

          {savedRule && (
            <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
              <h4 className="font-semibold text-green-800 dark:text-green-200 mb-2">
                ✅ Last Saved Rule
              </h4>
              <pre className="text-sm text-green-700 dark:text-green-300 overflow-auto">
                {JSON.stringify(savedRule, null, 2)}
              </pre>
            </div>
          )}

          <div className="space-y-2">
            <h4 className="font-semibold">Debug Logs</h4>
            <div className="h-64 overflow-y-auto p-3 bg-gray-50 dark:bg-gray-900 rounded border font-mono text-sm">
              {logs.length === 0 ? (
                <p className="text-gray-500">No logs yet. Click "Test Text Question Scoring" to start.</p>
              ) : (
                logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {showEditor && (
        <RuleEditor
          isOpen={showEditor}
          onClose={() => {
            addLog('❌ Rule editor closed without saving');
            setShowEditor(false);
          }}
          onSave={handleSaveRule}
          questionId="test-text-question-123"
          questionType={QuestionType.SHORT_TEXT}
          questionLabel="What is your company's competitive advantage?"
          isRepeatable={false}
          allowBonusRules={false}
        />
      )}
    </div>
  );
}
