"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  Filter, 
  FileText, 
  Zap,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface DealsHeaderProps {
  onSearchChange?: (search: string) => void;
  onFilterChange?: (filter: string) => void;
  activeFilter?: string;
  totalDeals?: number;
  dealCounts?: Record<string, number>;
}

// Backend-aligned filter options - these should match DealStatus enum exactly
const getFilterOptions = (dealCounts?: Record<string, number>) => [
  { value: 'all', label: 'All', count: dealCounts?.all || 0 },
  { value: 'new', label: 'New', count: dealCounts?.new || 0 },
  { value: 'triage', label: 'Active', count: dealCounts?.triage || 0 },
  { value: 'reviewed', label: 'Reviewed', count: dealCounts?.reviewed || 0 },
  { value: 'approved', label: 'Approved', count: dealCounts?.approved || 0 },
  { value: 'excluded', label: 'Flagged', count: dealCounts?.excluded || 0 },
  { value: 'closed', label: 'Completed', count: dealCounts?.closed || 0 },
];

export function DealsHeader({
  onSearchChange,
  onFilterChange,
  activeFilter = 'all',
  totalDeals = 0,
  dealCounts
}: DealsHeaderProps) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onSearchChange?.(value);
  };

  const handleFilterClick = (filter: string) => {
    onFilterChange?.(filter);
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const filterVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={headerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Main Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-4xl font-bold text-gray-900">Deal Management</h1>
          <p className="text-gray-500 mt-2 text-lg">Track and manage your investment opportunities</p>
        </div>
        
        {/* Right Side Actions - Minimal */}
        <div className="flex items-center gap-3">
          <Link href="/forms">
            <Button variant="ghost" size="sm" className="gap-2 text-gray-600 hover:text-gray-900">
              <FileText className="h-4 w-4" />
              Deal Forms
            </Button>
          </Link>
        </div>
      </div>

      {/* Search Bar - Full Width, Left-Aligned */}
      <div className="w-full">
        <div className="relative max-w-2xl">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search deals..."
            value={searchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 bg-white border-gray-200 shadow-sm w-full"
          />
        </div>
      </div>

      {/* Filter Chips - Minimal Design */}
      <motion.div
        className="flex flex-wrap gap-2"
        variants={filterVariants}
      >
        {getFilterOptions(dealCounts).map((filter, index) => {
          const isActive = activeFilter === filter.value;

          return (
            <motion.div
              key={filter.value}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.05, duration: 0.2 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleFilterClick(filter.value)}
                className={cn(
                  "rounded-full px-4 py-2 transition-all duration-200",
                  isActive
                    ? "bg-gray-900 text-white hover:bg-gray-800"
                    : "bg-gray-50 text-gray-700 hover:bg-gray-100"
                )}
              >
                {/* Status indicator dot for flagged items */}
                {filter.value === 'excluded' && (
                  <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
                )}

                <span className="font-medium">{filter.label}</span>
              </Button>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Minimal Stats Summary */}
      <motion.div
        className="flex items-center gap-4 text-sm text-gray-500"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <span>{totalDeals} deals total</span>
      </motion.div>
    </motion.div>
  );
}
