"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  Target,
  Users,
  TrendingUp,
  CheckCircle,
  ExternalLink,
  ChevronRight,
  Zap,
  AlertTriangle,
  Info,
  Star
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, SignalScore } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ScoreTabProps {
  deal: DealDetailData
}

const getScoreGradient = (score: number) => {
  if (score >= 80) return 'from-green-500 to-emerald-600'
  if (score >= 60) return 'from-yellow-500 to-orange-500'
  return 'from-red-500 to-red-600'
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-gradient-to-br from-green-50 to-emerald-50 border-green-200/50'
  if (score >= 60) return 'bg-gradient-to-br from-yellow-50 to-orange-50 border-yellow-200/50'
  return 'bg-gradient-to-br from-red-50 to-red-50 border-red-200/50'
}

const getInsightColor = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return 'text-green-600 bg-green-50 border-green-200'
    case 'negative': return 'text-red-600 bg-red-50 border-red-200'
    case 'neutral': return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  }
}

const getInsightIcon = (type: 'positive' | 'negative' | 'neutral') => {
  switch (type) {
    case 'positive': return CheckCircle
    case 'negative': return AlertTriangle
    case 'neutral': return Info
  }
}

const signals = [
  {
    id: 'team_strength',
    title: 'Team Strength',
    icon: Users,
    description: 'Founder experience, team composition, and track record'
  },
  {
    id: 'market_signals',
    title: 'Market Signals',
    icon: TrendingUp,
    description: 'Market size, growth trends, and competitive landscape'
  },
  {
    id: 'thesis_match',
    title: 'Thesis Match',
    icon: Target,
    description: 'Alignment with investment criteria and strategic focus'
  }
]

export function ScoreTab({ deal }: ScoreTabProps) {
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)
  const [animatedScore, setAnimatedScore] = useState(0)
  const scoreBreakdown = deal.score_breakdown

  // Animate score on mount
  useEffect(() => {
    if (scoreBreakdown?.overall_score) {
      const timer = setTimeout(() => {
        setAnimatedScore(scoreBreakdown.overall_score)
      }, 300)
      return () => clearTimeout(timer)
    }
  }, [scoreBreakdown?.overall_score])

  const handleViewFullAnalysis = () => {
    // Navigate to full analysis page
    window.location.href = `/deals/${deal.id}/full-analysis`
  }

  if (!scoreBreakdown) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No scoring data available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been scored yet. Scoring will appear here once the analysis is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Score Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-3">
              <Zap className="h-6 w-6 text-primary" />
              <h2 className="text-2xl font-bold text-gray-900">Overall Score</h2>
            </div>
            <p className="text-sm text-muted-foreground">
              Last updated {new Date(scoreBreakdown.last_updated).toLocaleDateString()}
            </p>
          </div>
          <Button
            onClick={handleViewFullAnalysis}
            size="sm"
            className="gap-2"
          >
            View Full Analysis
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Score Display Card */}
        <Card className={cn("p-6", getScoreBackground(scoreBreakdown.overall_score))}>
          <CardContent className="p-0">
            <div className="flex items-center justify-between">
              <div className="space-y-4">
                <div className="space-y-2">
                  <motion.span
                    className="text-4xl font-bold text-gray-900"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.8 }}
                  >
                    {animatedScore}
                  </motion.span>
                  <p className="text-sm font-medium text-muted-foreground">
                    AI-Generated Score
                  </p>
                </div>
                
                <div className="w-64">
                  <Progress
                    value={animatedScore}
                    className="h-2"
                  />
                </div>
              </div>
              
              {scoreBreakdown.ai_summary && (
                <div className="max-w-md">
                  <p className="text-base text-gray-700 italic">
                    "{scoreBreakdown.ai_summary}"
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Signal Analysis Section */}
      <div className="space-y-6">
        <h3 className="text-xl font-semibold text-gray-900">Signal Analysis</h3>
        
                 <div className="grid gap-4 md:grid-cols-3">
           {signals.map((signal) => {
             const signalData = scoreBreakdown.signal_breakdown[signal.id as keyof typeof scoreBreakdown.signal_breakdown]
             const Icon = signal.icon
             
             return (
               <motion.div
                 key={signal.id}
                 initial={{ opacity: 0, y: 20 }}
                 animate={{ opacity: 1, y: 0 }}
                 transition={{ duration: 0.3 }}
                 onClick={() => setSelectedSignal(signal.id)}
                 className="cursor-pointer"
               >
                 <Card className="hover:shadow-md transition-shadow">
                   <CardContent className="p-6">
                     <div className="space-y-4">
                       <div className="flex items-center gap-3">
                         <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-primary/10 text-primary">
                           <Icon className="h-5 w-5" />
                         </div>
                         <div className="flex-1">
                           <h4 className="font-medium text-base">{signal.title}</h4>
                           <p className="text-sm text-muted-foreground">
                             {signal.description}
                           </p>
                         </div>
                       </div>
                       
                       {signalData && (
                         <div className="space-y-2">
                           <div className="flex items-center justify-between">
                             <span className="text-sm font-medium">Score</span>
                             <span className="text-lg font-bold">{signalData.score}</span>
                           </div>
                           <Progress value={signalData.score} className="h-2" />
                         </div>
                       )}
                     </div>
                   </CardContent>
                 </Card>
               </motion.div>
             )
           })}
         </div>
      </div>

             {/* Key Insights Section */}
       {scoreBreakdown.key_insights && scoreBreakdown.key_insights.length > 0 && (
         <div className="space-y-6">
           <h3 className="text-xl font-semibold text-gray-900">Key Insights</h3>
           
           <div className="space-y-3">
             {scoreBreakdown.key_insights.map((insight, index) => {
               const Icon = getInsightIcon(insight.type)
               
               return (
                 <motion.div
                   key={index}
                   initial={{ opacity: 0, x: -20 }}
                   animate={{ opacity: 1, x: 0 }}
                   transition={{ duration: 0.3, delay: index * 0.1 }}
                 >
                   <Card className={cn("border", getInsightColor(insight.type))}>
                     <CardContent className="p-4">
                       <div className="flex items-start gap-3">
                         <Icon className="h-5 w-5 mt-0.5" />
                         <p className="text-base leading-relaxed">{insight.message}</p>
                       </div>
                     </CardContent>
                   </Card>
                 </motion.div>
               )
             })}
           </div>
         </div>
       )}

      {/* Signal Details Modal */}
      <Dialog open={!!selectedSignal} onOpenChange={() => setSelectedSignal(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3">
              {selectedSignal && (
                <>
                  {(() => {
                    const signal = signals.find(s => s.id === selectedSignal)
                    const Icon = signal?.icon || Target
                    return <Icon className="h-6 w-6" />
                  })()}
                  {signals.find(s => s.id === selectedSignal)?.title} Analysis
                </>
              )}
            </DialogTitle>
          </DialogHeader>
          
          {selectedSignal && (() => {
            const signalData = scoreBreakdown.signal_breakdown[selectedSignal as keyof typeof scoreBreakdown.signal_breakdown]
            return signalData ? (
              <div className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Score</span>
                    <span className="text-2xl font-bold">{signalData.score}</span>
                  </div>
                  <Progress value={signalData.score} className="h-3" />
                </div>
                
                                 {signalData.ai_insights && (
                   <div className="space-y-3">
                     <h4 className="font-semibold">Detailed Insights</h4>
                     <div className="p-4 bg-muted/50 rounded-lg">
                       <p className="text-sm leading-relaxed">{signalData.ai_insights}</p>
                     </div>
                   </div>
                 )}
              </div>
            ) : (
              <p className="text-muted-foreground">No detailed analysis available for this signal.</p>
            )
          })()}
        </DialogContent>
      </Dialog>
    </div>
  )
}
