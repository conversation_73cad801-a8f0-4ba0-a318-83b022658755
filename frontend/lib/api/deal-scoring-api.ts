// API client for deal scoring operations
import apiClient from '@/lib/api-client';

export interface ScoreOverrideRequest {
  signal_type: string;
  new_score: number;
  reason: string;
}

export interface FullAnalysisResponse {
  deal_id: string;
  overall_score: number;
  signal_breakdown: Record<string, any>;
  thesis_breakdown?: Record<string, any>;
  scoring_history: Array<Record<string, any>>;
  ai_explanations: Record<string, string>;
  sources: Record<string, Array<Record<string, any>>>;
}

export interface ScoringHistoryResponse {
  deal_id: string;
  history: Array<Record<string, any>>;
}

export const DealScoringAPI = {
  /**
   * Get comprehensive scoring analysis for a deal
   */
  async getFullAnalysis(dealId: string): Promise<FullAnalysisResponse> {
    try {
      console.log(`Fetching full analysis for deal ${dealId}`);
      const response = await apiClient.get(`/deals/${dealId}/full-analysis`);
      console.log('Full analysis fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching full analysis:', error);
      
      // TODO: Remove mock data when backend is implemented
      // For now, return mock data for demo
      return {
        deal_id: dealId,
        overall_score: 85,
        signal_breakdown: {
          team_strength: {
            score: 90,
            explanation: "Strong founding team with relevant experience",
            ai_insights: "Founders have proven track record with 2 exits totaling $150M+",
            sources: [
              { title: "CEO LinkedIn", url: "https://linkedin.com/in/ceo", type: "linkedin" },
              { title: "Crunchbase Profile", url: "https://crunchbase.com/company", type: "crunchbase" }
            ],
            sub_scores: {
              prior_exits: 95,
              domain_expertise: 85,
              team_composition: 90
            }
          },
          market_signals: {
            score: 82,
            explanation: "Growing market with positive trends",
            ai_insights: "Market showing 40% YoY growth with strong investor interest",
            sources: [
              { title: "Industry Report 2024", url: "https://example.com/report", type: "report" },
              { title: "Market News", url: "https://example.com/news", type: "news" }
            ],
            sub_scores: {
              market_size: 85,
              growth_rate: 90,
              competition: 70
            }
          },
          thesis_match: {
            score: 83,
            explanation: "Good alignment with investment thesis",
            ai_insights: "85% match to A2D Thesis with strong alignment on AI automation",
            sub_scores: {
              sector_match: 90,
              stage_fit: 80,
              geography: 85
            }
          }
        },
        thesis_breakdown: {
          thesis_id: "thesis123",
          thesis_name: "A2D Investment Thesis",
          rules: [
            {
              question_id: "q1",
              question_label: "Company Stage",
              answer: "Series A",
              expected: "Series A",
              match: true,
              weight: 10,
              points: 10
            },
            {
              question_id: "q2",
              question_label: "Sector",
              answer: "AI/ML",
              expected: "AI/ML",
              match: true,
              weight: 15,
              points: 15
            }
          ],
          total_score: 83,
          max_possible_score: 100
        },
        scoring_history: [
          {
            date: "2024-01-15T10:00:00Z",
            event: "Initial scoring",
            score: 85,
            type: "system",
            details: "Automated scoring from thesis evaluation"
          }
        ],
        ai_explanations: {
          team_strength: "The founding team demonstrates exceptional experience with multiple successful exits and deep domain expertise in AI/ML.",
          market_signals: "Market analysis shows strong growth trajectory with 40% YoY expansion and increasing investor interest.",
          thesis_match: "Strong alignment with investment criteria, particularly in AI automation and B2B focus areas."
        },
        sources: {
          team_strength: [
            { title: "CEO LinkedIn Profile", url: "https://linkedin.com/in/ceo", type: "linkedin" },
            { title: "CTO GitHub", url: "https://github.com/cto", type: "other" }
          ],
          market_signals: [
            { title: "Market Research Report", url: "https://example.com/report", type: "report" },
            { title: "Industry Analysis", url: "https://example.com/analysis", type: "news" }
          ]
        }
      };
    }
  },

  /**
   * Override a specific signal score for a deal
   */
  async overrideScore(dealId: string, request: ScoreOverrideRequest): Promise<any> {
    try {
      console.log(`Overriding score for deal ${dealId}:`, request);
      const response = await apiClient.post(`/deals/${dealId}/score-override`, request);
      console.log('Score override response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error overriding score:', error);
      
      // TODO: Remove mock response when backend is implemented
      // For now, return mock success for demo
      return {
        success: true,
        message: `Score override applied for ${request.signal_type}`,
        new_score: request.new_score,
        reason: request.reason,
        overridden_by: "<EMAIL>",
        timestamp: new Date().toISOString()
      };
    }
  },

  /**
   * Get scoring change history for a deal
   */
  async getScoringHistory(dealId: string): Promise<ScoringHistoryResponse> {
    try {
      console.log(`Fetching scoring history for deal ${dealId}`);
      const response = await apiClient.get(`/deals/${dealId}/scoring-history`);
      console.log('Scoring history fetched:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching scoring history:', error);
      
      // TODO: Remove mock data when backend is implemented
      // For now, return mock data for demo
      return {
        deal_id: dealId,
        history: [
          {
            date: "2024-01-15T10:00:00Z",
            event: "Initial scoring",
            score: 85,
            type: "system",
            details: "Automated scoring from thesis evaluation"
          },
          {
            date: "2024-01-15T12:00:00Z",
            event: "Score override",
            score: 90,
            type: "manual",
            user: "<EMAIL>",
            reason: "Updated after founder interview",
            signal: "team_strength"
          }
        ]
      };
    }
  }
};
